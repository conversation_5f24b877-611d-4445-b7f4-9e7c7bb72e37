package main

import (
	"context"
	"flag"
	"fmt"
	"log"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/initializer"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/activity_cashback"
)

func main() {
	var configPath string
	var userIDStr string
	flag.StringVar(&configPath, "config", "config.yaml", "Path to config file")
	flag.StringVar(&userIDStr, "user", "", "User ID to debug (required)")
	flag.Parse()

	if userIDStr == "" {
		log.Fatal("Please provide user ID with -user flag")
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		log.Fatalf("Invalid user ID: %v", err)
	}

	// Initialize the application
	global.GVA_VP = initializer.Viper(configPath)
	global.GVA_LOG = initializer.Zap()
	global.GVA_DB = initializer.GormPgSql()

	if global.GVA_DB == nil {
		log.Fatal("Failed to initialize database connection")
	}

	fmt.Printf("🔍 Debugging MEME trade task for user: %s\n", userID.String())
	fmt.Println("============================================================")

	ctx := context.Background()
	service := activity_cashback.NewActivityCashbackService()
	processorManager := activity_cashback.NewTaskProcessorManager(service)

	// Step 1: Check if user exists in activity cashback system
	fmt.Println("\n📋 Step 1: Check user initialization...")
	tierInfo, err := service.GetUserTierInfo(ctx, userID)
	if err != nil {
		fmt.Printf("❌ User not initialized in activity cashback system: %v\n", err)
		fmt.Println("🔧 Initializing user...")
		if err := service.InitializeUserForActivityCashback(ctx, userID); err != nil {
			log.Fatalf("Failed to initialize user: %v", err)
		}
		fmt.Println("✅ User initialized successfully")

		// Get tier info again
		tierInfo, err = service.GetUserTierInfo(ctx, userID)
		if err != nil {
			log.Fatalf("Failed to get tier info after initialization: %v", err)
		}
	} else {
		fmt.Println("✅ User already initialized in activity cashback system")
	}
	fmt.Printf("💰 Current Points: %d\n", tierInfo.TotalPoints)

	// Step 2: Check available tasks
	fmt.Println("\n📋 Step 2: Check available MEME trade tasks...")

	// Check daily category
	dailyTasks, err := service.GetTasksByCategory(ctx, model.CategoryDaily)
	if err != nil {
		log.Fatalf("Failed to get daily tasks: %v", err)
	}

	var dailyMemeTask *model.ActivityTask
	for _, task := range dailyTasks {
		if task.TaskIdentifier != nil && *task.TaskIdentifier == model.TaskIDMemeTradeDaily {
			dailyMemeTask = &task
			break
		}
	}

	if dailyMemeTask != nil {
		fmt.Printf("✅ Found MEME trade task in DAILY category: %s (ID: %s, Points: %d)\n",
			dailyMemeTask.Name, dailyMemeTask.ID.String(), dailyMemeTask.Points)
	} else {
		fmt.Println("❌ No MEME trade task found in DAILY category")
	}

	// Check trading category
	tradingTasks, err := service.GetTasksByCategory(ctx, model.CategoryTrading)
	if err != nil {
		log.Fatalf("Failed to get trading tasks: %v", err)
	}

	var tradingMemeTask *model.ActivityTask
	for _, task := range tradingTasks {
		if task.TaskIdentifier != nil && *task.TaskIdentifier == model.TaskIDMemeTradeDaily {
			tradingMemeTask = &task
			break
		}
	}

	if tradingMemeTask != nil {
		fmt.Printf("✅ Found MEME trade task in TRADING category: %s (ID: %s, Points: %d)\n",
			tradingMemeTask.Name, tradingMemeTask.ID.String(), tradingMemeTask.Points)
	} else {
		fmt.Println("❌ No MEME trade task found in TRADING category")
	}

	// Step 3: Check task progress
	fmt.Println("\n📊 Step 3: Check current task progress...")

	if dailyMemeTask != nil {
		progress, err := service.GetTaskProgress(ctx, userID, dailyMemeTask.ID)
		if err != nil {
			fmt.Printf("❌ No progress found for daily MEME task: %v\n", err)
		} else {
			fmt.Printf("📈 Daily MEME task progress: Status=%s, Count=%d, LastCompleted=%v\n",
				progress.Status, progress.CompletionCount, progress.LastCompletedAt)
		}
	}

	if tradingMemeTask != nil {
		progress, err := service.GetTaskProgress(ctx, userID, tradingMemeTask.ID)
		if err != nil {
			fmt.Printf("❌ No progress found for trading MEME task: %v\n", err)
		} else {
			fmt.Printf("📈 Trading MEME task progress: Status=%s, Count=%d, LastCompleted=%v\n",
				progress.Status, progress.CompletionCount, progress.LastCompletedAt)
		}
	}

	// Step 4: Simulate MEME trade processing
	fmt.Println("\n🚀 Step 4: Simulate MEME trade processing...")

	tradeData := map[string]interface{}{
		"trade_type": "MEME",
		"volume":     100.0,
		"order_id":   uuid.New().String(),
		"user_id":    userID.String(),
	}

	fmt.Printf("💱 Simulating trade data: %+v\n", tradeData)

	initialPoints := tierInfo.TotalPoints

	if err := processorManager.ProcessTradingEvent(ctx, userID, tradeData); err != nil {
		fmt.Printf("❌ Failed to process trading event: %v\n", err)
	} else {
		fmt.Println("✅ Trading event processed successfully")
	}

	// Step 5: Check results
	fmt.Println("\n🔍 Step 5: Check results after processing...")

	// Check points
	finalTierInfo, err := service.GetUserTierInfo(ctx, userID)
	if err != nil {
		log.Fatalf("Failed to get final tier info: %v", err)
	}

	pointsAwarded := finalTierInfo.TotalPoints - initialPoints
	fmt.Printf("💰 Points before: %d\n", initialPoints)
	fmt.Printf("💰 Points after: %d\n", finalTierInfo.TotalPoints)
	fmt.Printf("🎁 Points awarded: %d\n", pointsAwarded)

	// Check task progress again
	if dailyMemeTask != nil {
		progress, err := service.GetTaskProgress(ctx, userID, dailyMemeTask.ID)
		if err != nil {
			fmt.Printf("❌ No progress found for daily MEME task: %v\n", err)
		} else {
			fmt.Printf("📈 Daily MEME task progress after: Status=%s, Count=%d, LastCompleted=%v\n",
				progress.Status, progress.CompletionCount, progress.LastCompletedAt)
		}
	}

	if tradingMemeTask != nil {
		progress, err := service.GetTaskProgress(ctx, userID, tradingMemeTask.ID)
		if err != nil {
			fmt.Printf("❌ No progress found for trading MEME task: %v\n", err)
		} else {
			fmt.Printf("📈 Trading MEME task progress after: Status=%s, Count=%d, LastCompleted=%v\n",
				progress.Status, progress.CompletionCount, progress.LastCompletedAt)
		}
	}

	// Step 6: Summary
	fmt.Println("\n============================================================")
	fmt.Println("📋 SUMMARY:")

	if pointsAwarded > 0 {
		fmt.Printf("✅ SUCCESS: %d points were awarded\n", pointsAwarded)
	} else {
		fmt.Println("❌ ISSUE: No points were awarded")
		fmt.Println("🔍 Possible causes:")
		fmt.Println("   - Task already completed today")
		fmt.Println("   - Task not found in expected category")
		fmt.Println("   - Logic error in task processing")
		fmt.Println("   - Database transaction issue")
	}

	fmt.Println("============================================================")
}
