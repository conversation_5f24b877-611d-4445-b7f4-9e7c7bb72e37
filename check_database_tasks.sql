-- Check MEME trade tasks in database
-- Run this to verify tasks exist and have correct TaskIdentifier

-- 1. Check all task categories
SELECT id, name, description FROM activity_task_categories ORDER BY id;

-- 2. Check all tasks with TaskIdentifier
SELECT 
    t.id,
    t.name,
    t.task_identifier,
    t.points,
    t.frequency,
    c.name as category_name,
    t.is_available,
    t.sort_order
FROM activity_tasks t
JOIN activity_task_categories c ON t.category_id = c.id
WHERE t.task_identifier IS NOT NULL
ORDER BY c.name, t.sort_order;

-- 3. Check specifically for MEME trade tasks
SELECT 
    t.id,
    t.name,
    t.task_identifier,
    t.points,
    t.frequency,
    c.name as category_name,
    t.is_available
FROM activity_tasks t
JOIN activity_task_categories c ON t.category_id = c.id
WHERE t.task_identifier = 'MEME_TRADE_DAILY'
ORDER BY c.name;

-- 4. Check all tasks in trading category
SELECT 
    t.id,
    t.name,
    t.task_identifier,
    t.points,
    t.frequency,
    t.is_available,
    t.sort_order
FROM activity_tasks t
JOIN activity_task_categories c ON t.category_id = c.id
WHERE c.name = 'trading'
ORDER BY t.sort_order;

-- 5. Check all tasks in daily category
SELECT 
    t.id,
    t.name,
    t.task_identifier,
    t.points,
    t.frequency,
    t.is_available,
    t.sort_order
FROM activity_tasks t
JOIN activity_task_categories c ON t.category_id = c.id
WHERE c.name = 'daily'
ORDER BY t.sort_order;

-- 6. Check user task progress for a specific user (replace USER_ID)
-- SELECT 
--     p.id,
--     p.user_id,
--     t.name as task_name,
--     t.task_identifier,
--     c.name as category_name,
--     p.status,
--     p.completion_count,
--     p.last_completed_at,
--     p.created_at,
--     p.updated_at
-- FROM user_task_progress p
-- JOIN activity_tasks t ON p.task_id = t.id
-- JOIN activity_task_categories c ON t.category_id = c.id
-- WHERE p.user_id = 'USER_ID'
-- ORDER BY c.name, t.sort_order;

-- 7. Check daily task completions for a specific user (replace USER_ID)
-- SELECT 
--     dtc.id,
--     dtc.user_id,
--     t.name as task_name,
--     t.task_identifier,
--     dtc.completion_date,
--     dtc.points_awarded,
--     dtc.verification_data,
--     dtc.created_at
-- FROM daily_task_completions dtc
-- JOIN activity_tasks t ON dtc.task_id = t.id
-- WHERE dtc.user_id = 'USER_ID'
-- ORDER BY dtc.completion_date DESC, dtc.created_at DESC;

-- 8. Check user tier info for a specific user (replace USER_ID)
-- SELECT 
--     user_id,
--     current_tier,
--     total_points,
--     points_this_month,
--     last_activity_at,
--     created_at,
--     updated_at
-- FROM user_tier_info
-- WHERE user_id = 'USER_ID';

-- Expected results:
-- 1. Should see 'daily' and 'trading' categories
-- 2. Should see tasks with task_identifier like 'MEME_TRADE_DAILY', 'PERPETUAL_TRADE_DAILY', etc.
-- 3. Should see MEME_TRADE_DAILY in both 'daily' and 'trading' categories (after fix)
-- 4. Trading category should have tasks with task_identifier (after fix)
-- 5. Daily category should have MEME_TRADE_DAILY task
