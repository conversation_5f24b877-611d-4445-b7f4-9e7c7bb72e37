package activity_cashback

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/activity_cashback"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// TaskManagementService implements TaskManagementServiceInterface
type TaskManagementService struct {
	taskRepo                 activity_cashback.ActivityTaskRepositoryInterface
	categoryRepo             activity_cashback.TaskCategoryRepositoryInterface
	progressRepo             activity_cashback.UserTaskProgressRepositoryInterface
	completionFactory        *activity_cashback.TaskCompletionRepositoryFactory
	pendingCommunityTaskRepo activity_cashback.PendingCommunityTaskRepositoryInterface
	tierService              TierManagementServiceInterface
	progressService          TaskProgressServiceInterface
}

// NewTaskManagementService creates a new TaskManagementService
func NewTaskManagementService(
	taskRepo activity_cashback.ActivityTaskRepositoryInterface,
	categoryRepo activity_cashback.TaskCategoryRepositoryInterface,
	progressRepo activity_cashback.UserTaskProgressRepositoryInterface,
	tierService TierManagementServiceInterface,
	progressService TaskProgressServiceInterface,
) TaskManagementServiceInterface {
	return &TaskManagementService{
		taskRepo:                 taskRepo,
		categoryRepo:             categoryRepo,
		progressRepo:             progressRepo,
		completionFactory:        activity_cashback.NewTaskCompletionRepositoryFactory(),
		pendingCommunityTaskRepo: activity_cashback.NewPendingCommunityTaskRepository(),
		tierService:              tierService,
		progressService:          progressService,
	}
}

// CreateTask creates a new activity task
func (s *TaskManagementService) CreateTask(ctx context.Context, task *model.ActivityTask) error {
	// Validate task data
	if err := s.validateTask(task); err != nil {
		return fmt.Errorf("task validation failed: %w", err)
	}

	// Create the task
	if err := s.taskRepo.Create(ctx, task); err != nil {
		global.GVA_LOG.Error("Failed to create task", zap.Error(err), zap.String("task_name", task.Name))
		return fmt.Errorf("failed to create task: %w", err)
	}

	global.GVA_LOG.Info("Task created successfully", zap.String("task_id", task.ID.String()), zap.String("task_name", task.Name))
	return nil
}

// UpdateTask updates an existing activity task
func (s *TaskManagementService) UpdateTask(ctx context.Context, task *model.ActivityTask) error {
	// Validate task data
	if err := s.validateTask(task); err != nil {
		return fmt.Errorf("task validation failed: %w", err)
	}

	// Update the task
	if err := s.taskRepo.Update(ctx, task); err != nil {
		global.GVA_LOG.Error("Failed to update task", zap.Error(err), zap.String("task_id", task.ID.String()))
		return fmt.Errorf("failed to update task: %w", err)
	}

	global.GVA_LOG.Info("Task updated successfully", zap.String("task_id", task.ID.String()), zap.String("task_name", task.Name))
	return nil
}

// DeleteTask soft deletes an activity task
func (s *TaskManagementService) DeleteTask(ctx context.Context, taskID uuid.UUID) error {
	if err := s.taskRepo.Delete(ctx, taskID); err != nil {
		global.GVA_LOG.Error("Failed to delete task", zap.Error(err), zap.String("task_id", taskID.String()))
		return fmt.Errorf("failed to delete task: %w", err)
	}

	global.GVA_LOG.Info("Task deleted successfully", zap.String("task_id", taskID.String()))
	return nil
}

// GetTaskByID retrieves a task by ID
func (s *TaskManagementService) GetTaskByID(ctx context.Context, taskID uuid.UUID) (*model.ActivityTask, error) {
	task, err := s.taskRepo.GetByID(ctx, taskID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("task not found: %s", taskID.String())
		}
		return nil, fmt.Errorf("failed to get task: %w", err)
	}
	return task, nil
}

// GetTasksForUser retrieves available tasks for a user
func (s *TaskManagementService) GetTasksForUser(ctx context.Context, userID uuid.UUID) ([]model.ActivityTask, error) {
	tasks, err := s.taskRepo.GetTasksForUser(ctx, userID)
	if err != nil {
		global.GVA_LOG.Error("Failed to get tasks for user", zap.Error(err), zap.String("user_id", userID.String()))
		return nil, fmt.Errorf("failed to get tasks for user: %w", err)
	}
	return tasks, nil
}

// GetTasksByCategory retrieves tasks by category name
func (s *TaskManagementService) GetTasksByCategory(ctx context.Context, categoryName model.TaskCategoryName) ([]model.ActivityTask, error) {
	category, err := s.categoryRepo.GetByName(ctx, categoryName)
	if err != nil {
		return nil, fmt.Errorf("failed to get category: %w", err)
	}

	tasks, err := s.taskRepo.GetByCategoryID(ctx, category.ID)
	if err != nil {
		return nil, fmt.Errorf("failed to get tasks by category: %w", err)
	}
	return tasks, nil
}

// CompleteTask completes a task for a user
func (s *TaskManagementService) CompleteTask(ctx context.Context, userID, taskID uuid.UUID, verificationData map[string]interface{}) error {
	// Get the task
	task, err := s.GetTaskByID(ctx, taskID)
	if err != nil {
		return err
	}

	// Check if task is available
	if !task.IsAvailable() {
		return fmt.Errorf("task is not available")
	}

	// Verify task completion
	verified, err := s.VerifyTaskCompletion(ctx, userID, taskID, verificationData)
	if err != nil {
		return fmt.Errorf("task verification failed: %w", err)
	}
	if !verified {
		return fmt.Errorf("task completion could not be verified")
	}

	// Get or create user progress
	progress, err := s.progressService.GetTaskProgress(ctx, userID, taskID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			progress, err = s.progressService.InitializeTaskProgress(ctx, userID, taskID)
			if err != nil {
				return fmt.Errorf("failed to initialize task progress: %w", err)
			}
		} else {
			return fmt.Errorf("failed to get task progress: %w", err)
		}
	}

	// Check if task can be completed again
	if !s.canCompleteTask(ctx, userID, task, progress) {
		return fmt.Errorf("task cannot be completed at this time")
	}

	// Complete the progress
	if err := s.progressService.CompleteProgress(ctx, userID, taskID); err != nil {
		return fmt.Errorf("failed to complete progress: %w", err)
	}

	// Add points to user
	if err := s.tierService.AddPoints(ctx, userID, task.Points, fmt.Sprintf("task_completion:%s", taskID.String())); err != nil {
		global.GVA_LOG.Error("Failed to add points for task completion", zap.Error(err), zap.String("user_id", userID.String()), zap.String("task_id", taskID.String()))
		// Don't return error here as the task is already completed
	}

	// Record completion history using factory pattern
	if err := s.completionFactory.CreateTaskCompletion(ctx, userID, taskID, task.Points, verificationData); err != nil {
		global.GVA_LOG.Error("Failed to create completion history", zap.Error(err))
		// Don't return error here as the task is already completed
	}

	// Check for tier upgrade
	if _, err := s.tierService.CheckTierUpgrade(ctx, userID); err != nil {
		global.GVA_LOG.Error("Failed to check tier upgrade", zap.Error(err), zap.String("user_id", userID.String()))
		// Don't return error here as the task is already completed
	}

	global.GVA_LOG.Info("Task completed successfully",
		zap.String("user_id", userID.String()),
		zap.String("task_id", taskID.String()),
		zap.Int("points_awarded", task.Points))

	return nil
}

// CompleteTaskWithPoints completes a task and awards points automatically
// This method is designed for use by task handlers to ensure points are awarded
// when tasks are completed via automated systems (like NATS events)
func (s *TaskManagementService) CompleteTaskWithPoints(ctx context.Context, userID, taskID uuid.UUID, verificationData map[string]interface{}) error {
	// Get the task
	task, err := s.GetTaskByID(ctx, taskID)
	if err != nil {
		return fmt.Errorf("failed to get task: %w", err)
	}

	// Check if task is available
	if !task.IsAvailable() {
		return fmt.Errorf("task is not available")
	}

	// Get or create user progress
	progress, err := s.progressService.GetTaskProgress(ctx, userID, taskID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			progress, err = s.progressService.InitializeTaskProgress(ctx, userID, taskID)
			if err != nil {
				return fmt.Errorf("failed to initialize task progress: %w", err)
			}
		} else {
			return fmt.Errorf("failed to get task progress: %w", err)
		}
	}

	// Check if task can be completed again
	if !s.canCompleteTask(ctx, userID, task, progress) {
		global.GVA_LOG.Debug("Task cannot be completed at this time",
			zap.String("user_id", userID.String()),
			zap.String("task_id", taskID.String()),
			zap.String("task_name", task.Name))
		return nil // Don't return error, just skip silently for automated completion
	}

	// Complete the progress
	if err := s.progressService.CompleteProgress(ctx, userID, taskID); err != nil {
		return fmt.Errorf("failed to complete progress: %w", err)
	}

	// Add points to user
	if err := s.tierService.AddPoints(ctx, userID, task.Points, fmt.Sprintf("auto_task_completion:%s", taskID.String())); err != nil {
		global.GVA_LOG.Error("Failed to add points for automated task completion",
			zap.Error(err),
			zap.String("user_id", userID.String()),
			zap.String("task_id", taskID.String()),
			zap.String("task_name", task.Name),
			zap.Int("points", task.Points))
		// Don't return error here as the task is already completed
	}

	// Record completion history using factory pattern
	if verificationData == nil {
		verificationData = make(map[string]interface{})
	}
	verificationData["completion_method"] = "automated"
	verificationData["source"] = "task_handler"

	if err := s.completionFactory.CreateTaskCompletion(ctx, userID, taskID, task.Points, verificationData); err != nil {
		global.GVA_LOG.Error("Failed to create completion history for automated task", zap.Error(err))
		// Don't return error here as the task is already completed
	}

	// Check for tier upgrade
	if _, err := s.tierService.CheckTierUpgrade(ctx, userID); err != nil {
		global.GVA_LOG.Error("Failed to check tier upgrade", zap.Error(err), zap.String("user_id", userID.String()))
		// Don't return error here as the task is already completed
	}

	global.GVA_LOG.Info("Task completed automatically with points awarded",
		zap.String("user_id", userID.String()),
		zap.String("task_id", taskID.String()),
		zap.String("task_name", task.Name),
		zap.Int("points", task.Points))

	return nil
}

// validateTask validates task data
func (s *TaskManagementService) validateTask(task *model.ActivityTask) error {
	if task.Name == "" {
		return fmt.Errorf("task name is required")
	}
	if task.CategoryID == 0 {
		return fmt.Errorf("category ID is required")
	}
	if task.Points < 0 {
		return fmt.Errorf("points cannot be negative")
	}
	if task.StartDate != nil && task.EndDate != nil && task.StartDate.After(*task.EndDate) {
		return fmt.Errorf("start date cannot be after end date")
	}
	return nil
}

// canCompleteTask checks if a task can be completed based on its category and user progress
func (s *TaskManagementService) canCompleteTask(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, progress *model.UserTaskProgress) bool {
	switch task.Category.Name {
	case model.CategoryDaily:
		// For daily tasks, check both progress and completion history
		// First check progress table
		if progress.LastCompletedAt != nil {
			today := time.Now().Truncate(24 * time.Hour)
			lastCompleted := progress.LastCompletedAt.Truncate(24 * time.Hour)
			if today.Equal(lastCompleted) {
				return false // Already completed today based on progress
			}
		}

		// Also check completion history table for daily tasks
		if task.Frequency == model.FrequencyDaily {
			dailyRepo := s.completionFactory.GetRepositoryByFrequency(model.FrequencyDaily)
			if repo, ok := dailyRepo.(activity_cashback.DailyTaskCompletionRepositoryInterface); ok {
				completed, err := repo.HasUserCompletedTaskToday(ctx, userID, task.ID)
				if err != nil {
					global.GVA_LOG.Error("Failed to check daily completion", zap.Error(err))
					return false // Err on the side of caution
				}
				return !completed
			}
		}
		return true
	case model.CategoryCommunity:
		// Community tasks can be completed based on their frequency
		switch task.Frequency {
		case model.FrequencyOneTime:
			return progress.Status != model.TaskStatusCompleted && progress.Status != model.TaskStatusClaimed
		case model.FrequencyDaily:
			// Same logic as daily tasks
			if progress.LastCompletedAt != nil {
				today := time.Now().Truncate(24 * time.Hour)
				lastCompleted := progress.LastCompletedAt.Truncate(24 * time.Hour)
				if today.Equal(lastCompleted) {
					return false
				}
			}
			return true
		case model.FrequencyUnlimited:
			return true
		default:
			return true
		}
	case model.CategoryTrading:
		// Trading tasks can be completed based on their frequency
		switch task.Frequency {
		case model.FrequencyOneTime:
			return progress.Status != model.TaskStatusCompleted && progress.Status != model.TaskStatusClaimed
		case model.FrequencyDaily:
			// Same logic as daily tasks
			if progress.LastCompletedAt != nil {
				today := time.Now().Truncate(24 * time.Hour)
				lastCompleted := progress.LastCompletedAt.Truncate(24 * time.Hour)
				if today.Equal(lastCompleted) {
					return false
				}
			}
			return true
		case model.FrequencyProgressive:
			return progress.Status != model.TaskStatusCompleted && progress.Status != model.TaskStatusClaimed
		case model.FrequencyUnlimited:
			return true
		default:
			return true
		}
	default:
		return false
	}
}

// UpdateTaskProgress updates task progress for a user
func (s *TaskManagementService) UpdateTaskProgress(ctx context.Context, userID, taskID uuid.UUID, progressValue int) error {
	return s.progressService.SetProgress(ctx, userID, taskID, progressValue)
}

// ClaimTaskReward claims reward for a completed task
func (s *TaskManagementService) ClaimTaskReward(ctx context.Context, userID, taskID uuid.UUID) error {
	progress, err := s.progressService.GetTaskProgress(ctx, userID, taskID)
	if err != nil {
		return fmt.Errorf("failed to get task progress: %w", err)
	}

	if !progress.CanBeClaimed() {
		return fmt.Errorf("task reward cannot be claimed")
	}

	// Update progress status to claimed
	progress.Status = model.TaskStatusClaimed
	if err := s.progressRepo.Update(ctx, progress); err != nil {
		return fmt.Errorf("failed to update progress status: %w", err)
	}

	global.GVA_LOG.Info("Task reward claimed successfully",
		zap.String("user_id", userID.String()),
		zap.String("task_id", taskID.String()))

	return nil
}

// RefreshUserTasks refreshes task list for a user
func (s *TaskManagementService) RefreshUserTasks(ctx context.Context, userID uuid.UUID) error {
	// Get all available tasks
	tasks, err := s.GetTasksForUser(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to get tasks for user: %w", err)
	}

	// Initialize progress for new tasks
	for _, task := range tasks {
		_, err := s.progressService.GetTaskProgress(ctx, userID, task.ID)
		if err == gorm.ErrRecordNotFound {
			if _, err := s.progressService.InitializeTaskProgress(ctx, userID, task.ID); err != nil {
				global.GVA_LOG.Error("Failed to initialize task progress", zap.Error(err),
					zap.String("user_id", userID.String()), zap.String("task_id", task.ID.String()))
			}
		}
	}

	// Reset tasks that need to be reset
	if err := s.resetUserTasksIfNeeded(ctx, userID); err != nil {
		global.GVA_LOG.Error("Failed to reset user tasks", zap.Error(err), zap.String("user_id", userID.String()))
	}

	return nil
}

// ResetDailyTasks resets all daily tasks
func (s *TaskManagementService) ResetDailyTasks(ctx context.Context) error {
	tasks, err := s.progressRepo.GetTasksNeedingReset(ctx, model.ResetDaily)
	if err != nil {
		return fmt.Errorf("failed to get daily tasks needing reset: %w", err)
	}

	for _, progress := range tasks {
		progress.Reset()
		if err := s.progressRepo.Update(ctx, &progress); err != nil {
			global.GVA_LOG.Error("Failed to reset daily task progress", zap.Error(err),
				zap.String("user_id", progress.UserID.String()), zap.String("task_id", progress.TaskID.String()))
		}
	}

	global.GVA_LOG.Info("Daily tasks reset completed", zap.Int("tasks_reset", len(tasks)))
	return nil
}

// ResetWeeklyTasks resets all weekly tasks
func (s *TaskManagementService) ResetWeeklyTasks(ctx context.Context) error {
	tasks, err := s.progressRepo.GetTasksNeedingReset(ctx, model.ResetWeekly)
	if err != nil {
		return fmt.Errorf("failed to get weekly tasks needing reset: %w", err)
	}

	for _, progress := range tasks {
		progress.Reset()
		if err := s.progressRepo.Update(ctx, &progress); err != nil {
			global.GVA_LOG.Error("Failed to reset weekly task progress", zap.Error(err),
				zap.String("user_id", progress.UserID.String()), zap.String("task_id", progress.TaskID.String()))
		}
	}

	global.GVA_LOG.Info("Weekly tasks reset completed", zap.Int("tasks_reset", len(tasks)))
	return nil
}

// ResetMonthlyTasks resets all monthly tasks
func (s *TaskManagementService) ResetMonthlyTasks(ctx context.Context) error {
	tasks, err := s.progressRepo.GetTasksNeedingReset(ctx, model.ResetMonthly)
	if err != nil {
		return fmt.Errorf("failed to get monthly tasks needing reset: %w", err)
	}

	for _, progress := range tasks {
		progress.Reset()
		if err := s.progressRepo.Update(ctx, &progress); err != nil {
			global.GVA_LOG.Error("Failed to reset monthly task progress", zap.Error(err),
				zap.String("user_id", progress.UserID.String()), zap.String("task_id", progress.TaskID.String()))
		}
	}

	global.GVA_LOG.Info("Monthly tasks reset completed", zap.Int("tasks_reset", len(tasks)))
	return nil
}

// resetUserTasksIfNeeded resets user tasks that need to be reset
func (s *TaskManagementService) resetUserTasksIfNeeded(ctx context.Context, userID uuid.UUID) error {
	userProgress, err := s.progressRepo.GetByUserID(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to get user progress: %w", err)
	}

	for _, progress := range userProgress {
		if progress.Task.ResetPeriod != nil && progress.ShouldReset(*progress.Task.ResetPeriod) {
			progress.Reset()
			if err := s.progressRepo.Update(ctx, &progress); err != nil {
				global.GVA_LOG.Error("Failed to reset task progress", zap.Error(err),
					zap.String("user_id", userID.String()), zap.String("task_id", progress.TaskID.String()))
			}
		}
	}

	return nil
}

// VerifyTaskCompletion verifies task completion based on task type and conditions
func (s *TaskManagementService) VerifyTaskCompletion(ctx context.Context, userID, taskID uuid.UUID, verificationData map[string]interface{}) (bool, error) {
	task, err := s.GetTaskByID(ctx, taskID)
	if err != nil {
		return false, err
	}

	// Get category to determine verification method
	category, err := s.categoryRepo.GetByID(ctx, task.CategoryID)
	if err != nil {
		return false, fmt.Errorf("failed to get task category: %w", err)
	}

	switch category.Name {
	case model.CategoryDaily:
		return s.verifyDailyTask(ctx, userID, task, verificationData)
	case model.CategoryCommunity:
		return s.VerifySocialMediaTask(ctx, userID, taskID, verificationData)
	case model.CategoryTrading:
		return s.VerifyTradingTask(ctx, userID, taskID, verificationData)
	default:
		return s.verifyGenericTask(ctx, userID, task, verificationData)
	}
}

// VerifySocialMediaTask verifies social media task completion
func (s *TaskManagementService) VerifySocialMediaTask(ctx context.Context, userID, taskID uuid.UUID, socialData map[string]interface{}) (bool, error) {
	task, err := s.GetTaskByID(ctx, taskID)
	if err != nil {
		return false, err
	}

	// For social media tasks, verification depends on the verification method
	if task.VerificationMethod == nil {
		return false, fmt.Errorf("verification method not specified for social media task")
	}

	switch *task.VerificationMethod {
	case model.VerificationClickVerify:
		// For click verify tasks, we trust the frontend verification
		return true, nil
	case model.VerificationManual:
		// For manual verification, mark as pending and return false for now
		// This would typically involve admin review
		return false, fmt.Errorf("manual verification required")
	case model.VerificationAuto:
		// For auto verification, check external APIs (Twitter, Telegram, etc.)
		return s.verifyExternalSocialMedia(ctx, userID, task, socialData)
	default:
		return false, fmt.Errorf("unsupported verification method")
	}
}

// VerifyTradingTask verifies trading task completion
func (s *TaskManagementService) VerifyTradingTask(ctx context.Context, userID, taskID uuid.UUID, tradingData map[string]interface{}) (bool, error) {
	task, err := s.GetTaskByID(ctx, taskID)
	if err != nil {
		return false, err
	}

	// Extract trading data
	volume, ok := tradingData["volume"].(float64)
	if !ok {
		return false, fmt.Errorf("trading volume not provided")
	}

	tradeCount, ok := tradingData["trade_count"].(int)
	if !ok {
		tradeCount = 1 // Default to 1 if not provided
	}

	// Check task conditions
	if task.Conditions != nil {
		if task.Conditions.MinTradingVolume != nil && volume < *task.Conditions.MinTradingVolume {
			return false, fmt.Errorf("trading volume requirement not met")
		}
		if task.Conditions.RequiredTradeCount != nil && tradeCount < *task.Conditions.RequiredTradeCount {
			return false, fmt.Errorf("trade count requirement not met")
		}
	}

	return true, nil
}

// verifyDailyTask verifies daily task completion
func (s *TaskManagementService) verifyDailyTask(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, verificationData map[string]interface{}) (bool, error) {
	// Use TaskIdentifier for better maintainability
	if task.TaskIdentifier == nil {
		// Fallback to generic verification for tasks without identifier
		return s.verifyGenericTask(ctx, userID, task, verificationData)
	}

	switch *task.TaskIdentifier {
	case model.TaskIDDailyCheckin:
		// Daily check-in is always valid if called
		return true, nil
	case model.TaskIDMemeTradeDaily, model.TaskIDPerpetualTradeDaily:
		// Verify trading activity
		return s.VerifyTradingTask(ctx, userID, task.ID, verificationData)
	case model.TaskIDMarketPageView:
		// Market check is always valid if called
		return true, nil
	case model.TaskIDConsecutiveCheckin, model.TaskIDConsecutiveTradingDays:
		// Consecutive tasks are handled by their specific processors
		// For verification, we trust the processor logic
		return true, nil
	default:
		return s.verifyGenericTask(ctx, userID, task, verificationData)
	}
}

// verifyGenericTask verifies generic task completion
func (s *TaskManagementService) verifyGenericTask(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, verificationData map[string]interface{}) (bool, error) {
	// For generic tasks, check basic conditions
	if task.Conditions == nil {
		return true, nil
	}

	// Add more generic verification logic here as needed
	return true, nil
}

// verifyExternalSocialMedia verifies social media tasks using external APIs
func (s *TaskManagementService) verifyExternalSocialMedia(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, socialData map[string]interface{}) (bool, error) {
	// This would integrate with Twitter API, Telegram API, etc.
	// For now, return true as a placeholder
	global.GVA_LOG.Info("External social media verification not implemented",
		zap.String("task_name", task.Name),
		zap.String("user_id", userID.String()))
	return true, nil
}

// CreatePendingCommunityTask creates a pending community task that will be completed after 2 minutes
func (s *TaskManagementService) CreatePendingCommunityTask(ctx context.Context, userID, taskID uuid.UUID, verificationData map[string]interface{}) (*model.PendingCommunityTask, error) {
	// Check if user already has a pending task for this task ID
	hasPending, err := s.pendingCommunityTaskRepo.HasPendingTask(ctx, userID, taskID)
	if err != nil {
		return nil, fmt.Errorf("failed to check existing pending task: %w", err)
	}
	if hasPending {
		return nil, fmt.Errorf("user already has a pending task for this activity")
	}

	// Get task to validate it exists and is a community task
	task, err := s.GetTaskByID(ctx, taskID)
	if err != nil {
		return nil, fmt.Errorf("failed to get task: %w", err)
	}

	// Validate this is a community task that requires 2-minute wait
	if !s.isCommunityTaskWithWait(task) {
		return nil, fmt.Errorf("task does not require 2-minute wait")
	}

	// Create pending task
	now := time.Now()
	completionTime := now.Add(2 * time.Minute)

	pendingTask := &model.PendingCommunityTask{
		UserID:         userID,
		TaskID:         taskID,
		Status:         model.PendingCommunityTaskStatusPending,
		ClickedAt:      now,
		CompletionTime: &completionTime,
	}

	// Set verification data if provided
	if verificationData != nil {
		pendingTask.SetVerificationData("click_verify", "frontend", verificationData)
	}

	if err := s.pendingCommunityTaskRepo.Create(ctx, pendingTask); err != nil {
		return nil, fmt.Errorf("failed to create pending community task: %w", err)
	}

	global.GVA_LOG.Info("Created pending community task",
		zap.String("user_id", userID.String()),
		zap.String("task_id", taskID.String()),
		zap.String("task_name", task.Name),
		zap.Time("completion_time", completionTime))

	return pendingTask, nil
}

// GetPendingCommunityTask gets a pending community task for a user and task
func (s *TaskManagementService) GetPendingCommunityTask(ctx context.Context, userID, taskID uuid.UUID) (*model.PendingCommunityTask, error) {
	return s.pendingCommunityTaskRepo.GetByUserAndTask(ctx, userID, taskID)
}

// HasPendingCommunityTask checks if a user has a pending community task
func (s *TaskManagementService) HasPendingCommunityTask(ctx context.Context, userID, taskID uuid.UUID) (bool, error) {
	return s.pendingCommunityTaskRepo.HasPendingTask(ctx, userID, taskID)
}

// ProcessPendingCommunityTasks processes all pending community tasks that are ready for completion
func (s *TaskManagementService) ProcessPendingCommunityTasks(ctx context.Context) error {
	// Get all pending tasks ready for completion
	readyTasks, err := s.pendingCommunityTaskRepo.GetReadyForCompletion(ctx)
	if err != nil {
		return fmt.Errorf("failed to get ready pending tasks: %w", err)
	}

	if len(readyTasks) == 0 {
		return nil
	}

	global.GVA_LOG.Info("Processing pending community tasks", zap.Int("count", len(readyTasks)))

	processedCount := 0
	for _, pendingTask := range readyTasks {
		if err := s.processSinglePendingTask(ctx, pendingTask); err != nil {
			global.GVA_LOG.Error("Failed to process pending task",
				zap.Error(err),
				zap.String("pending_task_id", pendingTask.ID.String()),
				zap.String("user_id", pendingTask.UserID.String()),
				zap.String("task_id", pendingTask.TaskID.String()))
			continue
		}
		processedCount++
	}

	global.GVA_LOG.Info("Completed processing pending community tasks",
		zap.Int("processed", processedCount),
		zap.Int("total", len(readyTasks)))

	return nil
}

// processSinglePendingTask processes a single pending community task
func (s *TaskManagementService) processSinglePendingTask(ctx context.Context, pendingTask *model.PendingCommunityTask) error {
	// Get the task to award points
	task, err := s.GetTaskByID(ctx, pendingTask.TaskID)
	if err != nil {
		return fmt.Errorf("failed to get task: %w", err)
	}

	// Complete the task progress
	if err := s.progressService.CompleteProgress(ctx, pendingTask.UserID, pendingTask.TaskID); err != nil {
		return fmt.Errorf("failed to complete task progress: %w", err)
	}

	// Add points to user
	if err := s.tierService.AddPoints(ctx, pendingTask.UserID, task.Points, fmt.Sprintf("pending_task_completion:%s", pendingTask.TaskID.String())); err != nil {
		global.GVA_LOG.Error("Failed to add points for pending task completion", zap.Error(err))
		// Don't return error here as the task is already completed
	}

	// Update points earned in progress
	progress, err := s.progressService.GetTaskProgress(ctx, pendingTask.UserID, pendingTask.TaskID)
	if err == nil {
		progress.PointsEarned = task.Points
		if err := s.progressRepo.Update(ctx, progress); err != nil {
			global.GVA_LOG.Error("Failed to update points earned", zap.Error(err))
		}
	}

	// Record completion history using factory pattern
	verificationData := map[string]interface{}{
		"method": "pending_completion",
		"source": "background_job",
	}
	if pendingTask.VerificationData != nil {
		verificationData["original_data"] = pendingTask.VerificationData
	}

	if err := s.completionFactory.CreateTaskCompletion(ctx, pendingTask.UserID, pendingTask.TaskID, task.Points, verificationData); err != nil {
		global.GVA_LOG.Error("Failed to create completion history for pending task", zap.Error(err))
		// Don't return error here as the task is already completed
	}

	// Mark pending task as completed
	pendingTask.MarkAsCompleted()
	if err := s.pendingCommunityTaskRepo.Update(ctx, pendingTask); err != nil {
		global.GVA_LOG.Error("Failed to update pending task status", zap.Error(err))
		// Don't return error here as the main task is already completed
	}

	global.GVA_LOG.Info("Successfully processed pending community task",
		zap.String("user_id", pendingTask.UserID.String()),
		zap.String("task_id", pendingTask.TaskID.String()),
		zap.String("task_name", task.Name),
		zap.Int("points_awarded", task.Points))

	return nil
}

// isCommunityTaskWithWait checks if a task is a community task that requires 2-minute wait
func (s *TaskManagementService) isCommunityTaskWithWait(task *model.ActivityTask) bool {
	// Use category-based detection for better maintainability
	result := task.Category.Name == model.CategoryCommunity &&
		task.TaskIdentifier != nil &&
		model.RequiresTwoMinuteWait(*task.TaskIdentifier)

	// Debug logging
	global.GVA_LOG.Debug("Checking if task requires 2-minute wait",
		zap.String("task_id", task.ID.String()),
		zap.String("task_name", task.Name),
		zap.String("category_name", string(task.Category.Name)),
		zap.Any("task_identifier", task.TaskIdentifier),
		zap.Bool("requires_wait", result))

	return result
}
