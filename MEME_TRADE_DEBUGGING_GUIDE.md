# MEME Trade Task Debugging Guide

## 🎯 **Problem Statement**
MEME trade tasks are not completing automatically and points are not being awarded when users perform trades via NATS events.

## 🔍 **Debugging Steps**

### Step 1: Check Database Tasks
Run the SQL queries in `check_database_tasks.sql`:

```bash
psql -h localhost -U your_user -d your_db -f check_database_tasks.sql
```

**Expected Results:**
- ✅ Should see MEME_TRADE_DAILY task in 'daily' category
- ✅ Should see MEME_TRADE_DAILY task in 'trading' category (after fix)
- ❌ If trading category has no MEME_TRADE_DAILY task → **Root cause found**

### Step 2: Run Debug Script
Use the debug script to test task processing:

```bash
./debug_meme_trade_task -user "USER_ID_HERE"
```

**What it checks:**
1. User initialization in activity cashback system
2. Available MEME trade tasks in both categories
3. Current task progress
4. Simulates MEME trade processing
5. Checks results (points awarded, task completion)

**Expected Output:**
```
✅ Found MEME trade task in DAILY category: Complete one meme trade (Points: 200)
✅ Found MEME trade task in TRADING category: Complete one meme trade (Points: 200)
✅ Trading event processed successfully
🎁 Points awarded: 200
```

### Step 3: Check Application Logs
Look for these log messages when MEME trade occurs:

```
INFO Processing trading event user_id=... trade_type=MEME volume=...
INFO Processing MEME trade task user_id=... identifier=MEME_TRADE_DAILY
INFO Successfully processed MEME trade task in trading category user_id=...
INFO Task completed automatically with points awarded user_id=... points=200
```

**Red Flags:**
- `Failed to process MEME trade task in trading category, trying daily category`
- `Task cannot be completed at this time - already completed today`
- `Failed to complete MEME trade task with points`

### Step 4: Reseed Trading Tasks
If trading category is missing MEME tasks, run:

```bash
./reseed-trading-tasks
```

This will create missing trading tasks with TaskIdentifier.

## 🐛 **Common Issues & Solutions**

### Issue 1: Trading Category Missing MEME Tasks
**Symptoms:**
- Logs show "Failed to process MEME trade task in trading category"
- SQL query shows no MEME_TRADE_DAILY in trading category
- Always falls back to daily category

**Solution:**
1. Run `./reseed-trading-tasks`
2. Verify tasks created with SQL queries
3. Restart application

### Issue 2: Task Already Completed Today
**Symptoms:**
- Logs show "Task cannot be completed at this time - already completed today"
- No points awarded
- Task progress shows recent completion

**Solution:**
- This is expected behavior for daily tasks
- Test with different user or wait until next day
- For testing, manually reset task progress in database

### Issue 3: User Not Initialized
**Symptoms:**
- Debug script shows "User not initialized in activity cashback system"
- No tier info found

**Solution:**
- Debug script will auto-initialize user
- Or manually call `InitializeUserForActivityCashback`

### Issue 4: Points Not Awarded Despite Task Completion
**Symptoms:**
- Task marked as completed
- No points added to user
- Missing entry in daily_task_completions

**Solution:**
- Check if `CompleteTaskWithPoints` is being called (not just `CompleteProgress`)
- Verify tier service is working
- Check database transaction rollbacks

### Issue 5: NATS Events Not Processed
**Symptoms:**
- No logs about "Processing trading event"
- No task processing at all

**Solution:**
- Check NATS connection
- Verify affiliate subscriber is running
- Check if events are being published

## 🔧 **Manual Testing Commands**

### Test Task Processing Directly
```go
// In Go code or test
tradeData := map[string]interface{}{
    "trade_type": "MEME",
    "volume":     100.0,
    "order_id":   uuid.New().String(),
    "user_id":    userID.String(),
}

err := processorManager.ProcessTradingEvent(ctx, userID, tradeData)
```

### Check User Points Before/After
```sql
SELECT total_points FROM user_tier_info WHERE user_id = 'USER_ID';
```

### Check Task Completion Records
```sql
SELECT * FROM daily_task_completions 
WHERE user_id = 'USER_ID' 
AND completion_date = CURRENT_DATE
ORDER BY created_at DESC;
```

### Reset Task Progress (for testing)
```sql
-- Reset daily task progress
UPDATE user_task_progress 
SET status = 'not_started', 
    completion_count = 0, 
    last_completed_at = NULL 
WHERE user_id = 'USER_ID' 
AND task_id IN (
    SELECT id FROM activity_tasks 
    WHERE task_identifier = 'MEME_TRADE_DAILY'
);

-- Delete completion records
DELETE FROM daily_task_completions 
WHERE user_id = 'USER_ID' 
AND completion_date = CURRENT_DATE;
```

## 📊 **Verification Checklist**

After applying fixes, verify:

- [ ] **Database**: MEME_TRADE_DAILY task exists in both daily and trading categories
- [ ] **Logs**: Trading events are processed without falling back to daily category
- [ ] **Points**: Points are awarded automatically when task completes
- [ ] **Records**: Completion records are created in daily_task_completions
- [ ] **Progress**: Task progress is updated correctly
- [ ] **Tier**: User tier is checked for upgrades

## 🚨 **Emergency Rollback**

If fixes cause issues, quickly rollback by reverting task handlers:

```go
// Change back from:
if err := h.service.CompleteTaskWithPoints(ctx, userID, task.ID, verificationData); err != nil {

// To:
if err := h.service.CompleteProgress(ctx, userID, task.ID); err != nil {
```

## 📈 **Success Metrics**

Fix is successful when:
- ✅ MEME trades automatically complete tasks
- ✅ Points are awarded without manual API calls
- ✅ Logs show clear processing path
- ✅ No fallback to daily category needed
- ✅ Completion records are created
- ✅ User experience is seamless

## 🔄 **Continuous Monitoring**

Monitor these metrics:
- Task completion rate for MEME trades
- Point awarding success rate
- NATS event processing errors
- Fallback frequency to daily category
- User complaints about missing points
